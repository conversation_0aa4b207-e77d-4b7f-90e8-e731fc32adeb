{"timestamp": "2025-06-21T10:29:46.313292", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T10:29:46.332373", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T10:29:46.333941", "level": "ERROR", "logger": "app.core.database", "message": "数据库初始化失败: Not an executable object: 'SELECT 1'", "module": "database", "function": "init_database", "line": 79}
{"timestamp": "2025-06-21T10:33:11.563976", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T10:33:11.580372", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T10:33:11.580372", "level": "ERROR", "logger": "app.core.database", "message": "数据库初始化失败: Not an executable object: 'SELECT 1'", "module": "database", "function": "init_database", "line": 79}
{"timestamp": "2025-06-21T10:37:10.479724", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T10:37:10.496393", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T10:37:10.498399", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T10:40:41.407284", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T10:40:41.428773", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T10:40:41.429779", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:04:50.254047", "level": "ERROR", "logger": "app.services.auth_service", "message": "查询用户失败: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users \nWHERE users.email = ?\n LIMIT ? OFFSET ?]\n[parameters: ('<EMAIL>', 1, 0)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "auth_service", "function": "get_user_by_email", "line": 90}
{"timestamp": "2025-06-21T11:04:50.259226", "level": "ERROR", "logger": "app.services.auth_service", "message": "创建用户失败: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT count(*) AS count_1 \nFROM (SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users) AS anon_1]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "auth_service", "function": "create_user", "line": 134}
{"timestamp": "2025-06-21T11:04:50.260228", "level": "ERROR", "logger": "app.api.auth", "message": "用户注册失败: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT count(*) AS count_1 \nFROM (SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users) AS anon_1]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "auth", "function": "register", "line": 34}
{"timestamp": "2025-06-21T11:04:50.261248", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT count(*) AS count_1 \nFROM (SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users) AS anon_1]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:05:41.406152", "level": "ERROR", "logger": "app.api.auth", "message": "获取认证统计失败: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT count(*) AS count_1 \nFROM (SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users) AS anon_1]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "auth", "function": "get_auth_stats", "line": 183}
{"timestamp": "2025-06-21T11:05:41.407155", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 获取统计信息失败", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:06:31.581734", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:06:31.599309", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:06:31.600444", "level": "ERROR", "logger": "app.core.database", "message": "数据库初始化失败: name 'text' is not defined", "module": "database", "function": "init_database", "line": 79}
{"timestamp": "2025-06-21T11:09:57.090266", "level": "ERROR", "logger": "app.services.auth_service", "message": "查询用户失败: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users \nWHERE users.email = ?\n LIMIT ? OFFSET ?]\n[parameters: ('<EMAIL>', 1, 0)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "auth_service", "function": "get_user_by_email", "line": 90}
{"timestamp": "2025-06-21T11:09:57.092782", "level": "ERROR", "logger": "app.services.auth_service", "message": "创建用户失败: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT count(*) AS count_1 \nFROM (SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users) AS anon_1]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "auth_service", "function": "create_user", "line": 134}
{"timestamp": "2025-06-21T11:09:57.092782", "level": "ERROR", "logger": "app.api.auth", "message": "用户注册失败: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT count(*) AS count_1 \nFROM (SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users) AS anon_1]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "auth", "function": "register", "line": 34}
{"timestamp": "2025-06-21T11:09:57.093295", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT count(*) AS count_1 \nFROM (SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users) AS anon_1]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:11:15.267482", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:11:15.285333", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:11:15.286348", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:11:49.028231", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:11:49.046626", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:11:49.047633", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:12:05.963485", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:12:05.981470", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:12:05.982478", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:13:18.993153", "level": "ERROR", "logger": "app.services.auth_service", "message": "查询用户失败: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users \nWHERE users.email = ?\n LIMIT ? OFFSET ?]\n[parameters: ('<EMAIL>', 1, 0)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "auth_service", "function": "get_user_by_email", "line": 90}
{"timestamp": "2025-06-21T11:13:18.997215", "level": "ERROR", "logger": "app.services.auth_service", "message": "创建用户失败: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT count(*) AS count_1 \nFROM (SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users) AS anon_1]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "auth_service", "function": "create_user", "line": 134}
{"timestamp": "2025-06-21T11:13:18.997215", "level": "ERROR", "logger": "app.api.auth", "message": "用户注册失败: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT count(*) AS count_1 \nFROM (SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users) AS anon_1]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "auth", "function": "register", "line": 34}
{"timestamp": "2025-06-21T11:13:18.998729", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT count(*) AS count_1 \nFROM (SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users) AS anon_1]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:14:42.274437", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:14:42.346126", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:14:42.346126", "level": "ERROR", "logger": "app.core.database", "message": "数据库初始化失败: Not an executable object: 'SELECT 1'", "module": "database", "function": "init_database", "line": 79}
{"timestamp": "2025-06-21T11:15:05.930558", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:15:05.950507", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:15:05.950507", "level": "ERROR", "logger": "app.core.database", "message": "数据库初始化失败: Not an executable object: 'SELECT 1'", "module": "database", "function": "init_database", "line": 79}
{"timestamp": "2025-06-21T11:15:17.398347", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:15:17.416912", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:15:17.417922", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:15:33.660983", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:15:33.684963", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:15:33.685970", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:15:53.775225", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:15:53.794897", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:15:53.795894", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:17:25.464850", "level": "INFO", "logger": "app.services.auth_service", "message": "用户创建成功: <EMAIL>", "module": "auth_service", "function": "create_user", "line": 129}
{"timestamp": "2025-06-21T11:17:42.166548", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 未提供认证令牌", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:18:16.942571", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 未提供认证令牌", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:19:32.142260", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 认证失败", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:19:35.155192", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 认证失败", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:19:38.558044", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 未提供认证令牌", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:19:46.617609", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 未提供认证令牌", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:19:48.584754", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 未提供认证令牌", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:19:51.117939", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 未提供认证令牌", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:25:51.150629", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:25:51.173820", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:25:51.174912", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:25:52.324147", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:25:52.345535", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:25:52.346085", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:27:18.672092", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:27:18.692926", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:27:18.693933", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:27:19.849948", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:27:19.872076", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:27:19.872076", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:28:14.608815", "level": "INFO", "logger": "app.services.auth_service", "message": "用户创建成功: <EMAIL>", "module": "auth_service", "function": "create_user", "line": 129}
{"timestamp": "2025-06-21T11:28:45.835171", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 认证失败", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:19:23.535714", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T15:19:23.571254", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T15:19:23.571254", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T15:19:46.389052", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T15:19:46.389764", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:19:46.390271", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:21:06.077745", "level": "INFO", "logger": "app.services.auth_service", "message": "用户创建成功: <EMAIL>", "module": "auth_service", "function": "create_user", "line": 129}
{"timestamp": "2025-06-21T15:21:14.010102", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T15:21:14.012117", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: module 'jwt' has no attribute 'encode'", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:21:14.013120", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:26:32.852245", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T15:26:32.877266", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T15:26:32.878417", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T15:27:08.530023", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T15:27:08.530023", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:27:08.531539", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:27:21.086920", "level": "ERROR", "logger": "app.services.auth_service", "message": "创建用户失败: 邮箱已被注册", "module": "auth_service", "function": "create_user", "line": 134}
{"timestamp": "2025-06-21T15:27:21.086920", "level": "ERROR", "logger": "app.api.auth", "message": "用户注册失败: 邮箱已被注册", "module": "auth", "function": "register", "line": 34}
{"timestamp": "2025-06-21T15:27:21.088234", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱已被注册", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:27:31.385637", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T15:27:31.386630", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:27:31.387135", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:28:18.892361", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T15:28:38.991566", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T15:29:55.736959", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T15:35:51.380449", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T15:35:51.381521", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:35:51.382550", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:36:01.274001", "level": "ERROR", "logger": "app.services.auth_service", "message": "创建用户失败: 邮箱已被注册", "module": "auth_service", "function": "create_user", "line": 134}
{"timestamp": "2025-06-21T15:36:01.274994", "level": "ERROR", "logger": "app.api.auth", "message": "用户注册失败: 邮箱已被注册", "module": "auth", "function": "register", "line": 34}
{"timestamp": "2025-06-21T15:36:01.274994", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱已被注册", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:36:24.630370", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T15:36:24.631445", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:36:24.631445", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:36:53.844856", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T15:36:53.845860", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:36:53.845860", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:41:07.719481", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T15:41:07.741825", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T15:41:07.742836", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T15:41:47.915466", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T15:43:24.923352", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T15:44:15.504501", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T15:45:51.569103", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T15:45:51.569103", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:45:51.570608", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:46:02.878987", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T15:46:02.880997", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: module 'jwt' has no attribute 'encode'", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:46:02.882406", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:46:13.740286", "level": "ERROR", "logger": "app.services.auth_service", "message": "创建用户失败: 邮箱已被注册", "module": "auth_service", "function": "create_user", "line": 134}
{"timestamp": "2025-06-21T15:46:13.740286", "level": "ERROR", "logger": "app.api.auth", "message": "用户注册失败: 邮箱已被注册", "module": "auth", "function": "register", "line": 34}
{"timestamp": "2025-06-21T15:46:13.740286", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱已被注册", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:46:44.856069", "level": "INFO", "logger": "app.services.auth_service", "message": "用户创建成功: <EMAIL>", "module": "auth_service", "function": "create_user", "line": 129}
{"timestamp": "2025-06-21T15:46:50.812229", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T15:46:50.812229", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:46:50.813711", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:47:12.474714", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T15:47:12.475717", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: module 'jwt' has no attribute 'encode'", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:47:12.476778", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
